'use client';

import { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, useMap } from 'react-leaflet';
import { Icon } from 'leaflet';
import { MapPin, Navigation, Filter, X, Star, TrendingUp } from 'lucide-react';
import { parseCoordinates, getCurrentLocation, filterCollegesByDistance } from '../lib/geoService';
import { trackSearchInteraction } from '../lib/searchAnalytics';
import { useAuth } from '../hooks/useAuth';

// Fix for default markers in react-leaflet
import 'leaflet/dist/leaflet.css';

// Custom marker icons
const createCustomIcon = (color = 'blue', isSelected = false) => {
  const size = isSelected ? 35 : 25;
  return new Icon({
    iconUrl: `data:image/svg+xml;base64,${btoa(`
      <svg width="${size}" height="${size}" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7z" fill="${color}" stroke="white" stroke-width="2"/>
        <circle cx="12" cy="9" r="2.5" fill="white"/>
      </svg>
    `)}`,
    iconSize: [size, size],
    iconAnchor: [size / 2, size],
    popupAnchor: [0, -size]
  });
};

// Map bounds for Bangalore
const BANGALORE_BOUNDS = [
  [12.7, 77.3], // Southwest
  [13.2, 77.8]  // Northeast
];

const BANGALORE_CENTER = [12.9716, 77.5946];

export default function MapView({ 
  colleges = [], 
  onCollegeSelect,
  selectedCollegeId = null,
  showFilters = true,
  className = ''
}) {
  const [map, setMap] = useState(null);
  const [userLocation, setUserLocation] = useState(null);
  const [filteredColleges, setFilteredColleges] = useState(colleges);
  const [filters, setFilters] = useState({
    maxDistance: 50,
    minPlacementRate: 0,
    metroAccess: null,
    showUserLocation: false
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  
  const { user } = useAuth();

  // Update filtered colleges when colleges or filters change
  useEffect(() => {
    applyFilters();
  }, [colleges, filters, userLocation]);

  const applyFilters = () => {
    let filtered = [...colleges];

    // Filter by distance from user location
    if (userLocation && filters.maxDistance < 50) {
      filtered = filterCollegesByDistance(filtered, userLocation, filters.maxDistance);
    }

    // Filter by placement rate
    if (filters.minPlacementRate > 0) {
      filtered = filtered.filter(college => college.placementRate >= filters.minPlacementRate);
    }

    // Filter by metro access
    if (filters.metroAccess !== null) {
      filtered = filtered.filter(college => college.metroAccess === filters.metroAccess);
    }

    setFilteredColleges(filtered);
  };

  const handleGetUserLocation = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const location = await getCurrentLocation();
      setUserLocation(location);
      setFilters(prev => ({ ...prev, showUserLocation: true }));
      
      // Center map on user location
      if (map) {
        map.setView([location.lat, location.lng], 12);
      }
    } catch (error) {
      setError(error.message);
      console.error('Error getting user location:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCollegeClick = (college) => {
    if (onCollegeSelect) {
      onCollegeSelect(college);
    }

    // Track interaction
    trackSearchInteraction({
      query: 'map_view',
      collegeId: college.id,
      collegeName: college.name,
      action: 'view',
      userId: user?.uid
    });
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      maxDistance: 50,
      minPlacementRate: 0,
      metroAccess: null,
      showUserLocation: false
    });
    setUserLocation(null);
  };

  // Component to handle map events
  const MapEvents = () => {
    const map = useMap();
    
    useEffect(() => {
      setMap(map);
    }, [map]);

    return null;
  };

  const getMarkerColor = (college) => {
    if (college.id === selectedCollegeId) return '#ef4444'; // red for selected
    if (college.ranking <= 10) return '#10b981'; // green for top 10
    if (college.placementRate >= 90) return '#3b82f6'; // blue for high placement
    return '#6b7280'; // gray for others
  };

  return (
    <div className={`relative ${className}`}>
      {/* Map Filters */}
      {showFilters && (
        <div className="absolute top-4 left-4 z-[1000] bg-white rounded-lg shadow-lg p-4 max-w-sm">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-900">Map Filters</h3>
            <button
              onClick={clearFilters}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-4 w-4" />
            </button>
          </div>

          <div className="space-y-3">
            {/* User Location */}
            <div>
              <button
                onClick={handleGetUserLocation}
                disabled={isLoading}
                className="w-full flex items-center justify-center px-3 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 disabled:opacity-50 text-sm"
              >
                <Navigation className="h-4 w-4 mr-2" />
                {isLoading ? 'Getting Location...' : 'Find Colleges Near Me'}
              </button>
              {error && (
                <p className="text-red-600 text-xs mt-1">{error}</p>
              )}
            </div>

            {/* Distance Filter */}
            {userLocation && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Max Distance: {filters.maxDistance}km
                </label>
                <input
                  type="range"
                  min="5"
                  max="50"
                  value={filters.maxDistance}
                  onChange={(e) => handleFilterChange('maxDistance', parseInt(e.target.value))}
                  className="w-full"
                />
              </div>
            )}

            {/* Placement Rate Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Min Placement Rate
              </label>
              <select
                value={filters.minPlacementRate}
                onChange={(e) => handleFilterChange('minPlacementRate', parseInt(e.target.value))}
                className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm"
              >
                <option value={0}>Any</option>
                <option value={70}>70%+</option>
                <option value={80}>80%+</option>
                <option value={90}>90%+</option>
              </select>
            </div>

            {/* Metro Access Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Metro Access
              </label>
              <select
                value={filters.metroAccess === null ? '' : filters.metroAccess.toString()}
                onChange={(e) => handleFilterChange('metroAccess', e.target.value === '' ? null : e.target.value === 'true')}
                className="w-full border border-gray-300 rounded-md px-2 py-1 text-sm"
              >
                <option value="">Any</option>
                <option value="true">Metro Accessible</option>
                <option value="false">No Metro Access</option>
              </select>
            </div>
          </div>

          <div className="mt-3 pt-3 border-t border-gray-200">
            <p className="text-xs text-gray-600">
              Showing {filteredColleges.length} of {colleges.length} colleges
            </p>
          </div>
        </div>
      )}

      {/* Map Legend */}
      <div className="absolute top-4 right-4 z-[1000] bg-white rounded-lg shadow-lg p-3">
        <h4 className="font-medium text-gray-900 mb-2 text-sm">Legend</h4>
        <div className="space-y-1 text-xs">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
            <span>Top 10 Ranked</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
            <span>90%+ Placement</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
            <span>Selected</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-gray-500 rounded-full mr-2"></div>
            <span>Others</span>
          </div>
        </div>
      </div>

      {/* Map Container */}
      <MapContainer
        center={BANGALORE_CENTER}
        zoom={11}
        style={{ height: '100%', width: '100%' }}
        maxBounds={BANGALORE_BOUNDS}
        maxBoundsViscosity={1.0}
      >
        <MapEvents />
        
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {/* User Location Marker */}
        {userLocation && filters.showUserLocation && (
          <Marker
            position={[userLocation.lat, userLocation.lng]}
            icon={createCustomIcon('#ef4444', true)}
          >
            <Popup>
              <div className="text-center">
                <strong>Your Location</strong>
                <br />
                <small>Accuracy: ±{Math.round(userLocation.accuracy)}m</small>
              </div>
            </Popup>
          </Marker>
        )}

        {/* College Markers */}
        {filteredColleges.map((college) => {
          const coordinates = parseCoordinates(college.coordinates);
          if (!coordinates) return null;

          const isSelected = college.id === selectedCollegeId;
          const markerColor = getMarkerColor(college);

          return (
            <Marker
              key={college.id}
              position={[coordinates.lat, coordinates.lng]}
              icon={createCustomIcon(markerColor, isSelected)}
              eventHandlers={{
                click: () => handleCollegeClick(college)
              }}
            >
              <Popup>
                <div className="min-w-[250px]">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-gray-900 text-sm">{college.name}</h3>
                    <span className="bg-primary-100 text-primary-800 px-2 py-1 rounded-full text-xs">
                      #{college.ranking}
                    </span>
                  </div>
                  
                  <div className="space-y-1 text-xs text-gray-600">
                    <div className="flex items-center">
                      <Star className="h-3 w-3 mr-1" />
                      <span>{college.placementRate}% Placement Rate</span>
                    </div>
                    <div className="flex items-center">
                      <TrendingUp className="h-3 w-3 mr-1" />
                      <span>₹{college.highestPackage} LPA Highest Package</span>
                    </div>
                    {college.metroAccess && (
                      <div className="flex items-center">
                        <MapPin className="h-3 w-3 mr-1" />
                        <span>Metro Accessible</span>
                      </div>
                    )}
                    {userLocation && (
                      <div className="text-primary-600">
                        Distance: {college.distance ? `${college.distance}km` : 'Calculating...'}
                      </div>
                    )}
                  </div>

                  <button
                    onClick={() => handleCollegeClick(college)}
                    className="w-full mt-2 bg-primary-600 text-white px-3 py-1 rounded text-xs hover:bg-primary-700"
                  >
                    View Details
                  </button>
                </div>
              </Popup>
            </Marker>
          );
        })}
      </MapContainer>
    </div>
  );
}
