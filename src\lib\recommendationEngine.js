import { getAllColleges } from './collegeData';
import { calculateDistance, parseCoordinates } from './geoService';
import { collection, addDoc, query, where, getDocs, orderBy, limit as firestoreLimit, serverTimestamp } from 'firebase/firestore';
import { db } from './firebase';

// Calculate similarity score between two colleges (updated for current data structure)
const calculateCollegeSimilarity = (college1, college2) => {
  let score = 0;
  let factors = 0;

  // Location similarity (based on coordinates and metro access)
  const coords1 = parseCoordinates(college1.coordinates);
  const coords2 = parseCoordinates(college2.coordinates);
  if (coords1 && coords2) {
    const distance = calculateDistance(coords1, coords2);
    const locationScore = Math.max(0, 1 - (distance / 50)); // Closer = more similar
    score += locationScore * 0.2;
  } else if (college1.metroAccess === college2.metroAccess) {
    score += 0.2; // Same metro access
  }
  factors += 0.2;

  // Ranking proximity (closer rankings are more similar)
  const rankingDiff = Math.abs(college1.ranking - college2.ranking);
  const rankingScore = Math.max(0, 1 - (rankingDiff / 50)); // Normalize to 0-1
  score += rankingScore * 0.25;
  factors += 0.25;

  // Placement rate similarity
  const placementDiff = Math.abs(college1.placementRate - college2.placementRate);
  const placementScore = Math.max(0, 1 - (placementDiff / 100));
  score += placementScore * 0.2;
  factors += 0.2;

  // Package similarity
  const packageDiff = Math.abs(college1.highestPackage - college2.highestPackage);
  const packageScore = Math.max(0, 1 - (packageDiff / 100));
  score += packageScore * 0.15;
  factors += 0.15;

  // Course overlap (text-based similarity)
  const courseSimilarity = calculateTextSimilarity(college1.coursesOffered, college2.coursesOffered);
  score += courseSimilarity * 0.2;
  factors += 0.2;

  return factors > 0 ? score / factors : 0;
};

// Calculate text similarity using Jaccard similarity
const calculateTextSimilarity = (text1, text2) => {
  if (!text1 || !text2) return 0;

  const words1 = new Set(text1.toLowerCase().split(/\s+/));
  const words2 = new Set(text2.toLowerCase().split(/\s+/));

  const intersection = new Set([...words1].filter(x => words2.has(x)));
  const union = new Set([...words1, ...words2]);

  return union.size > 0 ? intersection.size / union.size : 0;
};

// Calculate user preference score for a college
const calculatePreferenceScore = (college, userPreferences) => {
  if (!userPreferences) return 0.5; // Default neutral score

  let score = 0;
  let factors = 0;

  // Budget preference
  if (userPreferences.budget) {
    const { min, max } = userPreferences.budget;
    if (college.fees.tuition >= min && college.fees.tuition <= max) {
      score += 1;
    } else {
      // Partial score based on how close it is to the range
      const distance = Math.min(
        Math.abs(college.fees.tuition - min),
        Math.abs(college.fees.tuition - max)
      );
      const maxDistance = Math.max(min, max);
      score += Math.max(0, 1 - (distance / maxDistance));
    }
    factors += 1;
  }

  // Program preference
  if (userPreferences.programs && userPreferences.programs.length > 0) {
    const userPrograms = new Set(userPreferences.programs.map(p => p.toLowerCase()));
    const collegePrograms = new Set(college.courses.map(c => c.toLowerCase()));
    const hasMatchingProgram = [...userPrograms].some(p => 
      [...collegePrograms].some(c => c.includes(p.toLowerCase()) || p.toLowerCase().includes(c))
    );
    score += hasMatchingProgram ? 1 : 0;
    factors += 1;
  }

  // Location preference
  if (userPreferences.location) {
    const prefLocation = userPreferences.location.toLowerCase();
    const collegeLocation = college.location.toLowerCase();
    if (collegeLocation.includes(prefLocation) || prefLocation.includes(collegeLocation)) {
      score += 1;
    }
    factors += 1;
  }

  // College size preference
  if (userPreferences.collegeSize && userPreferences.collegeSize !== 'any') {
    const studentCount = college.studentCount || 0;
    let sizeMatch = false;
    
    switch (userPreferences.collegeSize) {
      case 'small':
        sizeMatch = studentCount < 5000;
        break;
      case 'medium':
        sizeMatch = studentCount >= 5000 && studentCount <= 15000;
        break;
      case 'large':
        sizeMatch = studentCount > 15000;
        break;
    }
    
    score += sizeMatch ? 1 : 0;
    factors += 1;
  }

  return factors > 0 ? score / factors : 0.5;
};

// Get content-based recommendations
export const getContentBasedRecommendations = async (userId, limit = 6) => {
  try {
    const [allColleges, userFavorites] = await Promise.all([
      getAllColleges(),
      getUserFavorites(userId)
    ]);

    if (userFavorites.length === 0) {
      // No favorites yet, return popular colleges
      return allColleges
        .sort((a, b) => a.ranking - b.ranking)
        .slice(0, limit)
        .map(college => ({ ...college, score: 0.5, reason: 'Popular choice' }));
    }

    // Get favorite college IDs
    const favoriteIds = new Set(userFavorites.map(fav => fav.collegeId));
    
    // Get favorite colleges data
    const favoriteColleges = allColleges.filter(college => favoriteIds.has(college.id));
    
    // Calculate recommendations based on similarity to favorites
    const recommendations = allColleges
      .filter(college => !favoriteIds.has(college.id)) // Exclude already favorited
      .map(college => {
        // Calculate average similarity to all favorite colleges
        const similarities = favoriteColleges.map(favCollege => 
          calculateCollegeSimilarity(college, favCollege)
        );
        const avgSimilarity = similarities.length > 0 
          ? similarities.reduce((sum, sim) => sum + sim, 0) / similarities.length 
          : 0;

        return {
          ...college,
          score: avgSimilarity,
          reason: `Similar to ${favoriteColleges[0]?.name || 'your favorites'}`
        };
      })
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);

    return recommendations;
  } catch (error) {
    console.error('Error getting content-based recommendations:', error);
    return [];
  }
};

// Get preference-based recommendations
export const getPreferenceBasedRecommendations = async (userId, limit = 6) => {
  try {
    const [allColleges, userProfile, userFavorites] = await Promise.all([
      getAllColleges(),
      getUserProfile(userId),
      getUserFavorites(userId)
    ]);

    const favoriteIds = new Set(userFavorites.map(fav => fav.collegeId));
    const userPreferences = userProfile?.preferences;

    const recommendations = allColleges
      .filter(college => !favoriteIds.has(college.id))
      .map(college => {
        const score = calculatePreferenceScore(college, userPreferences);
        return {
          ...college,
          score,
          reason: 'Matches your preferences'
        };
      })
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);

    return recommendations;
  } catch (error) {
    console.error('Error getting preference-based recommendations:', error);
    return [];
  }
};

// Get hybrid recommendations (combines content and preference-based)
export const getHybridRecommendations = async (userId, limit = 6) => {
  try {
    const [contentRecs, preferenceRecs] = await Promise.all([
      getContentBasedRecommendations(userId, limit * 2),
      getPreferenceBasedRecommendations(userId, limit * 2)
    ]);

    // Combine and deduplicate recommendations
    const combinedMap = new Map();

    // Add content-based recommendations with weight
    contentRecs.forEach(college => {
      combinedMap.set(college.id, {
        ...college,
        score: college.score * 0.6, // 60% weight for content-based
        reason: college.reason
      });
    });

    // Add preference-based recommendations with weight
    preferenceRecs.forEach(college => {
      if (combinedMap.has(college.id)) {
        // Combine scores if college appears in both lists
        const existing = combinedMap.get(college.id);
        combinedMap.set(college.id, {
          ...existing,
          score: existing.score + (college.score * 0.4), // 40% weight for preference-based
          reason: 'Matches your preferences and favorites'
        });
      } else {
        combinedMap.set(college.id, {
          ...college,
          score: college.score * 0.4,
          reason: college.reason
        });
      }
    });

    // Sort by combined score and return top recommendations
    return Array.from(combinedMap.values())
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);
  } catch (error) {
    console.error('Error getting hybrid recommendations:', error);
    return [];
  }
};

// Get trending colleges (based on recent favorites across all users)
export const getTrendingColleges = async (limit = 6) => {
  try {
    const allColleges = await getAllColleges();
    
    // For now, return top-ranked colleges as "trending"
    // In a real implementation, you'd query recent favorites across all users
    return allColleges
      .sort((a, b) => a.ranking - b.ranking)
      .slice(0, limit)
      .map(college => ({
        ...college,
        score: 1 - (college.ranking / 100), // Higher score for better ranking
        reason: 'Trending choice'
      }));
  } catch (error) {
    console.error('Error getting trending colleges:', error);
    return [];
  }
};

// Main recommendation function
export const getRecommendations = async (userId, type = 'hybrid', limit = 6) => {
  switch (type) {
    case 'content':
      return getContentBasedRecommendations(userId, limit);
    case 'preference':
      return getPreferenceBasedRecommendations(userId, limit);
    case 'trending':
      return getTrendingColleges(limit);
    case 'hybrid':
    default:
      return getHybridRecommendations(userId, limit);
  }
};
