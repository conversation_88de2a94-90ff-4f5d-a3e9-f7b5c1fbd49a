'use client';

import { useState, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { Search, Map, List, Grid, Filter, Sparkles, TrendingUp, Clock } from 'lucide-react';
import SmartSearch from '../../components/SmartSearch';
import MapView from '../../components/MapView';
import CollegeCard from '../../components/CollegeCard';
import LazyCollegeGrid from '../../components/ui/LazyCollegeGrid';
import { smartSearchColleges, getAllColleges } from '../../lib/collegeData';
import { getRecommendations } from '../../lib/recommendationEngine';
import { useAuth } from '../../hooks/useAuth';

export default function SearchPage() {
  const searchParams = useSearchParams();
  const { user } = useAuth();
  
  const [colleges, setColleges] = useState([]);
  const [searchResults, setSearchResults] = useState(null);
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchLoading, setSearchLoading] = useState(false);
  const [viewMode, setViewMode] = useState('grid'); // 'grid', 'list', 'map'
  const [selectedCollege, setSelectedCollege] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');

  // Initialize data
  useEffect(() => {
    const initializeData = async () => {
      try {
        setLoading(true);
        
        // Load all colleges
        const allColleges = await getAllColleges();
        setColleges(allColleges);
        
        // Load recommendations if user is logged in
        if (user) {
          const userRecommendations = await getRecommendations(user.uid, 'hybrid', 6);
          setRecommendations(userRecommendations);
        }
        
        // Check for initial search query from URL
        const initialQuery = searchParams.get('q');
        if (initialQuery) {
          setSearchQuery(initialQuery);
          await handleSearch(initialQuery);
        }
        
      } catch (error) {
        console.error('Error initializing search page:', error);
      } finally {
        setLoading(false);
      }
    };

    initializeData();
  }, [user, searchParams]);

  const handleSearch = async (query, options = {}) => {
    if (!query.trim()) {
      setSearchResults(null);
      return;
    }

    setSearchLoading(true);
    try {
      const results = await smartSearchColleges(query, {
        ...options,
        userId: user?.uid,
        trackAnalytics: true
      });
      
      setSearchResults(results);
      setSearchQuery(query);
    } catch (error) {
      console.error('Search error:', error);
    } finally {
      setSearchLoading(false);
    }
  };

  const handleCollegeSelect = (college) => {
    setSelectedCollege(college);
    if (viewMode !== 'map') {
      // Scroll to college details or navigate
      window.open(`/colleges/${college.id}`, '_blank');
    }
  };

  const getDisplayColleges = () => {
    if (searchResults) {
      return searchResults.results;
    }
    return colleges;
  };

  const displayColleges = getDisplayColleges();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading search interface...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container-max py-6">
          <div className="text-center mb-6">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
              Smart College Search
            </h1>
            <p className="text-lg text-gray-600">
              Find your perfect engineering college with AI-powered search
            </p>
          </div>

          {/* Smart Search */}
          <div className="max-w-4xl mx-auto">
            <SmartSearch
              onSearch={handleSearch}
              initialQuery={searchQuery}
              placeholder="Ask me anything about engineering colleges... (e.g., 'Show me top colleges near Electronic City with good placements')"
              className="w-full"
            />
          </div>
        </div>
      </div>

      <div className="container-max py-6">
        {/* Search Results Insights */}
        {searchResults && (
          <div className="bg-gradient-to-r from-primary-50 to-blue-50 border border-primary-200 rounded-lg p-4 mb-6">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="font-semibold text-primary-900 mb-2 flex items-center">
                  <Sparkles className="h-5 w-5 mr-2" />
                  Search Insights
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-primary-700 font-medium">Intent:</span>
                    <span className="ml-2 px-2 py-1 bg-primary-100 text-primary-800 rounded-full text-xs">
                      {searchResults.intent}
                    </span>
                  </div>
                  <div>
                    <span className="text-primary-700 font-medium">Results:</span>
                    <span className="ml-2 text-primary-600">{searchResults.totalCount} colleges</span>
                  </div>
                  <div>
                    <span className="text-primary-700 font-medium">Search Time:</span>
                    <span className="ml-2 text-primary-600">{searchResults.searchTime}ms</span>
                  </div>
                  <div>
                    <span className="text-primary-700 font-medium">Confidence:</span>
                    <span className="ml-2 text-primary-600">
                      {Math.round((searchResults.confidence || 0) * 100)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* View Mode Controls */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              {displayColleges.length} College{displayColleges.length !== 1 ? 's' : ''} Found
            </h2>
            {searchQuery && (
              <p className="text-gray-600 mt-1">
                Results for "{searchQuery}"
                {searchResults && (
                  <span className="ml-2 text-primary-600 text-sm">
                    (Smart Search)
                  </span>
                )}
              </p>
            )}
          </div>

          <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-md transition-colors duration-200 ${
                viewMode === 'grid'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              aria-label="Grid view"
            >
              <Grid className="h-5 w-5" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-md transition-colors duration-200 ${
                viewMode === 'list'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              aria-label="List view"
            >
              <List className="h-5 w-5" />
            </button>
            <button
              onClick={() => setViewMode('map')}
              className={`p-2 rounded-md transition-colors duration-200 ${
                viewMode === 'map'
                  ? 'bg-white text-primary-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
              aria-label="Map view"
            >
              <Map className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Results Display */}
        {searchLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Searching colleges...</p>
          </div>
        ) : displayColleges.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-gray-100 rounded-full w-24 h-24 flex items-center justify-center mx-auto mb-6">
              <Search className="h-12 w-12 text-gray-400" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">No colleges found</h3>
            <p className="text-gray-600 mb-6">
              Try adjusting your search query or use different keywords.
            </p>
            <button
              onClick={() => {
                setSearchQuery('');
                setSearchResults(null);
              }}
              className="btn-primary"
            >
              Clear Search
            </button>
          </div>
        ) : (
          <>
            {viewMode === 'map' ? (
              <div className="h-[600px] rounded-lg overflow-hidden shadow-lg">
                <MapView
                  colleges={displayColleges}
                  onCollegeSelect={handleCollegeSelect}
                  selectedCollegeId={selectedCollege?.id}
                  showFilters={true}
                />
              </div>
            ) : viewMode === 'grid' ? (
              <LazyCollegeGrid colleges={displayColleges} itemsPerPage={12} />
            ) : (
              <div className="space-y-6">
                {displayColleges.map((college) => (
                  <CollegeCard
                    key={college.id}
                    college={college}
                    isCompact={true}
                    showCompareButton={true}
                    onClick={() => handleCollegeSelect(college)}
                  />
                ))}
              </div>
            )}
          </>
        )}

        {/* Recommendations Section */}
        {!searchResults && recommendations.length > 0 && (
          <div className="mt-12 bg-white rounded-lg shadow-sm p-6">
            <div className="flex items-center mb-6">
              <TrendingUp className="h-6 w-6 text-primary-600 mr-2" />
              <h3 className="text-xl font-bold text-gray-900">Recommended for You</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {recommendations.map((college) => (
                <CollegeCard
                  key={college.id}
                  college={college}
                  showCompareButton={true}
                  onClick={() => handleCollegeSelect(college)}
                />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
