'use client';

import { useState, useEffect } from 'react';
import { MapPin, List, Filter, Navigation } from 'lucide-react';
import MapView from '../../components/MapView';
import CollegeCard from '../../components/CollegeCard';
import { getAllColleges } from '../../lib/collegeData';
import { getCurrentLocation, filterCollegesByDistance } from '../../lib/geoService';

export default function MapPage() {
  const [colleges, setColleges] = useState([]);
  const [filteredColleges, setFilteredColleges] = useState([]);
  const [selectedCollege, setSelectedCollege] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showSidebar, setShowSidebar] = useState(false);
  const [userLocation, setUserLocation] = useState(null);

  useEffect(() => {
    const loadColleges = async () => {
      try {
        const allColleges = await getAllColleges();
        setColleges(allColleges);
        setFilteredColleges(allColleges);
      } catch (error) {
        console.error('Error loading colleges:', error);
      } finally {
        setLoading(false);
      }
    };

    loadColleges();
  }, []);

  const handleCollegeSelect = (college) => {
    setSelectedCollege(college);
    setShowSidebar(true);
  };

  const handleGetNearbyColleges = async () => {
    try {
      const location = await getCurrentLocation();
      setUserLocation(location);
      
      const nearby = filterCollegesByDistance(colleges, location, 25);
      setFilteredColleges(nearby);
    } catch (error) {
      console.error('Error getting nearby colleges:', error);
      alert('Unable to get your location. Please enable location services.');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading map...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className="bg-white shadow-sm border-b p-4 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">College Map</h1>
          <p className="text-gray-600">Explore {colleges.length} engineering colleges in Bangalore</p>
        </div>
        
        <div className="flex items-center space-x-4">
          <button
            onClick={handleGetNearbyColleges}
            className="flex items-center px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
          >
            <Navigation className="h-4 w-4 mr-2" />
            Find Nearby
          </button>
          
          <button
            onClick={() => setShowSidebar(!showSidebar)}
            className="flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          >
            <List className="h-4 w-4 mr-2" />
            {showSidebar ? 'Hide' : 'Show'} List
          </button>
        </div>
      </div>

      <div className="flex-1 flex relative">
        {/* Map */}
        <div className={`${showSidebar ? 'w-2/3' : 'w-full'} transition-all duration-300`}>
          <MapView
            colleges={filteredColleges}
            onCollegeSelect={handleCollegeSelect}
            selectedCollegeId={selectedCollege?.id}
            showFilters={true}
            className="h-full"
          />
        </div>

        {/* Sidebar */}
        {showSidebar && (
          <div className="w-1/3 bg-white border-l border-gray-200 overflow-y-auto">
            <div className="p-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">
                  Colleges ({filteredColleges.length})
                </h3>
                <button
                  onClick={() => setShowSidebar(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
              {userLocation && (
                <p className="text-sm text-gray-600 mt-1">
                  Showing colleges near your location
                </p>
              )}
            </div>

            <div className="p-4 space-y-4">
              {filteredColleges.map((college) => (
                <div
                  key={college.id}
                  className={`cursor-pointer transition-all duration-200 ${
                    selectedCollege?.id === college.id
                      ? 'ring-2 ring-primary-500 rounded-lg'
                      : ''
                  }`}
                  onClick={() => handleCollegeSelect(college)}
                >
                  <CollegeCard
                    college={college}
                    isCompact={true}
                    showCompareButton={false}
                    className="hover:shadow-md"
                  />
                </div>
              ))}
              
              {filteredColleges.length === 0 && (
                <div className="text-center py-8">
                  <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No colleges found in this area</p>
                  <button
                    onClick={() => setFilteredColleges(colleges)}
                    className="mt-2 text-primary-600 hover:text-primary-700"
                  >
                    Show all colleges
                  </button>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Selected College Details Modal */}
        {selectedCollege && !showSidebar && (
          <div className="absolute bottom-4 left-4 right-4 bg-white rounded-lg shadow-lg p-4 max-w-md mx-auto">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900">{selectedCollege.name}</h3>
                <p className="text-primary-600 text-sm">{selectedCollege.acronym}</p>
                <div className="mt-2 grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Rank:</span>
                    <span className="ml-1 font-medium">#{selectedCollege.ranking}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Placement:</span>
                    <span className="ml-1 font-medium">{selectedCollege.placementRate}%</span>
                  </div>
                </div>
                <div className="mt-3 flex space-x-2">
                  <a
                    href={`/colleges/${selectedCollege.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="px-3 py-1 bg-primary-600 text-white text-sm rounded hover:bg-primary-700 transition-colors"
                  >
                    View Details
                  </a>
                  <button
                    onClick={() => setSelectedCollege(null)}
                    className="px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded hover:bg-gray-200 transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
