'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Star, TrendingUp, MapPin, Users, Award, ExternalLink, Heart, Compare } from 'lucide-react';
import OptimizedImage from './ui/OptimizedImage';
import { formatCurrency, formatNIRF } from '../lib/collegeData';

export default function RecommendationCard({ 
  college, 
  showReason = true,
  showScore = false,
  onCompare,
  onFavorite,
  className = ''
}) {
  const [isHovered, setIsHovered] = useState(false);

  const getScoreColor = (score) => {
    if (score >= 0.8) return 'text-green-600 bg-green-100';
    if (score >= 0.6) return 'text-blue-600 bg-blue-100';
    if (score >= 0.4) return 'text-yellow-600 bg-yellow-100';
    return 'text-gray-600 bg-gray-100';
  };

  const getReasonIcon = (reason) => {
    if (reason?.includes('Similar')) return <Star className="h-4 w-4" />;
    if (reason?.includes('preference')) return <Heart className="h-4 w-4" />;
    if (reason?.includes('Trending')) return <TrendingUp className="h-4 w-4" />;
    return <Award className="h-4 w-4" />;
  };

  return (
    <div 
      className={`bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden border border-gray-200 ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Image Section */}
      <div className="relative h-48 w-full">
        <OptimizedImage
          src={college.image}
          alt={`${college.name} campus`}
          width={400}
          height={192}
          className="w-full h-full object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
        
        {/* Ranking Badge */}
        <div className="absolute top-3 left-3">
          <div className="bg-primary-600 text-white px-2 py-1 rounded-full text-sm font-semibold">
            Rank #{college.ranking}
          </div>
        </div>

        {/* Score Badge */}
        {showScore && college.score !== undefined && (
          <div className="absolute top-3 right-3">
            <div className={`px-2 py-1 rounded-full text-sm font-semibold ${getScoreColor(college.score)}`}>
              {Math.round(college.score * 100)}% Match
            </div>
          </div>
        )}

        {/* Hover Overlay */}
        {isHovered && (
          <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center transition-opacity duration-200">
            <Link
              href={`/colleges/${college.id}`}
              className="bg-white text-gray-900 px-4 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors flex items-center"
            >
              View Details
              <ExternalLink className="h-4 w-4 ml-2" />
            </Link>
          </div>
        )}
      </div>

      {/* Content Section */}
      <div className="p-4">
        {/* Header */}
        <div className="mb-3">
          <h3 className="text-lg font-bold text-gray-900 mb-1 line-clamp-2">
            {college.name}
          </h3>
          <p className="text-primary-600 font-semibold">{college.acronym}</p>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 gap-3 mb-4">
          <div className="text-center p-2 bg-gray-50 rounded-lg">
            <div className="text-lg font-bold text-secondary-600">{college.placementRate}%</div>
            <div className="text-xs text-gray-500">Placement Rate</div>
          </div>
          <div className="text-center p-2 bg-gray-50 rounded-lg">
            <div className="text-lg font-bold text-green-600">₹{college.highestPackage}L</div>
            <div className="text-xs text-gray-500">Highest Package</div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="space-y-2 mb-4">
          <div className="flex items-center text-sm text-gray-600">
            <MapPin className="h-4 w-4 mr-2 text-gray-400" />
            <span className="truncate">
              {college.metroAccess ? 'Metro Accessible' : 'Limited Metro Access'}
            </span>
          </div>
          
          <div className="flex items-center text-sm text-gray-600">
            <Users className="h-4 w-4 mr-2 text-gray-400" />
            <span>Est. {college.establishedYear}</span>
            <span className="mx-2">•</span>
            <span>{college.campusSize}</span>
          </div>

          {college.nirf && college.nirf !== 'N/A' && (
            <div className="flex items-center text-sm text-gray-600">
              <Award className="h-4 w-4 mr-2 text-gray-400" />
              <span>NIRF Rank: {formatNIRF(college.nirf)}</span>
            </div>
          )}
        </div>

        {/* Recommendation Reason */}
        {showReason && college.reason && (
          <div className="mb-4 p-2 bg-primary-50 border border-primary-200 rounded-lg">
            <div className="flex items-center text-sm text-primary-700">
              {getReasonIcon(college.reason)}
              <span className="ml-2 font-medium">{college.reason}</span>
            </div>
          </div>
        )}

        {/* Reasons List */}
        {college.reasons && college.reasons.length > 0 && (
          <div className="mb-4">
            <div className="flex flex-wrap gap-1">
              {college.reasons.slice(0, 2).map((reason, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
                >
                  {reason}
                </span>
              ))}
              {college.reasons.length > 2 && (
                <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                  +{college.reasons.length - 2} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-100">
          <Link
            href={`/colleges/${college.id}`}
            className="text-primary-600 hover:text-primary-700 font-medium text-sm transition-colors"
          >
            View Details
          </Link>
          
          <div className="flex items-center space-x-2">
            {onFavorite && (
              <button
                onClick={() => onFavorite(college)}
                className="p-2 text-gray-400 hover:text-red-500 transition-colors"
                aria-label="Add to favorites"
              >
                <Heart className="h-4 w-4" />
              </button>
            )}
            
            {onCompare && (
              <button
                onClick={() => onCompare(college)}
                className="p-2 text-gray-400 hover:text-blue-500 transition-colors"
                aria-label="Add to comparison"
              >
                <Compare className="h-4 w-4" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
