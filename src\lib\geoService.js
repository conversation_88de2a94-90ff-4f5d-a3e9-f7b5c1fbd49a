// Geolocation and Distance Calculation Service
import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { db } from './firebase';

// Parse coordinates from string format "12.9237° N, 77.4987° E"
export const parseCoordinates = (coordString) => {
  if (!coordString || typeof coordString !== 'string') {
    return null;
  }

  try {
    // Extract latitude and longitude from the string
    const regex = /(\d+\.?\d*)°?\s*N,?\s*(\d+\.?\d*)°?\s*E/i;
    const match = coordString.match(regex);
    
    if (match) {
      return {
        lat: parseFloat(match[1]),
        lng: parseFloat(match[2])
      };
    }
    
    // Try alternative format: "lat, lng"
    const simpleRegex = /(\d+\.?\d*),?\s*(\d+\.?\d*)/;
    const simpleMatch = coordString.match(simpleRegex);
    
    if (simpleMatch) {
      return {
        lat: parseFloat(simpleMatch[1]),
        lng: parseFloat(simpleMatch[2])
      };
    }
    
    return null;
  } catch (error) {
    console.error('Error parsing coordinates:', error);
    return null;
  }
};

// Calculate distance between two points using Haversine formula
export const calculateDistance = (point1, point2) => {
  if (!point1 || !point2 || !point1.lat || !point1.lng || !point2.lat || !point2.lng) {
    return null;
  }

  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(point2.lat - point1.lat);
  const dLng = toRadians(point2.lng - point1.lng);
  
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadians(point1.lat)) * Math.cos(toRadians(point2.lat)) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  return Math.round(distance * 100) / 100; // Round to 2 decimal places
};

const toRadians = (degrees) => {
  return degrees * (Math.PI / 180);
};

// Get user's current location
export const getCurrentLocation = () => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser'));
      return;
    }

    const options = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 300000 // 5 minutes
    };

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          lat: position.coords.latitude,
          lng: position.coords.longitude,
          accuracy: position.coords.accuracy
        });
      },
      (error) => {
        let errorMessage = 'Unable to get location';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out';
            break;
        }
        reject(new Error(errorMessage));
      },
      options
    );
  });
};

// Filter colleges by distance from a point
export const filterCollegesByDistance = (colleges, centerPoint, maxDistance) => {
  if (!centerPoint || !maxDistance || !colleges) {
    return colleges;
  }

  return colleges
    .map(college => {
      const collegeCoords = parseCoordinates(college.coordinates);
      if (!collegeCoords) {
        return { ...college, distance: null };
      }

      const distance = calculateDistance(centerPoint, collegeCoords);
      return { ...college, distance };
    })
    .filter(college => college.distance !== null && college.distance <= maxDistance)
    .sort((a, b) => a.distance - b.distance);
};

// Get colleges near user's location
export const getCollegesNearMe = async (colleges, maxDistance = 25) => {
  try {
    const userLocation = await getCurrentLocation();
    
    // Track location usage for analytics
    await trackLocationUsage(userLocation, maxDistance);
    
    return filterCollegesByDistance(colleges, userLocation, maxDistance);
  } catch (error) {
    console.error('Error getting colleges near user:', error);
    throw error;
  }
};

// Predefined locations in Bangalore
export const BANGALORE_LOCATIONS = {
  'electronic_city': { lat: 12.8406, lng: 77.6635, name: 'Electronic City' },
  'whitefield': { lat: 12.9698, lng: 77.7500, name: 'Whitefield' },
  'koramangala': { lat: 12.9279, lng: 77.6271, name: 'Koramangala' },
  'indiranagar': { lat: 12.9719, lng: 77.6412, name: 'Indiranagar' },
  'jp_nagar': { lat: 12.9082, lng: 77.5833, name: 'JP Nagar' },
  'banashankari': { lat: 12.9255, lng: 77.5648, name: 'Banashankari' },
  'yelahanka': { lat: 13.1007, lng: 77.5963, name: 'Yelahanka' },
  'hebbal': { lat: 13.0358, lng: 77.5970, name: 'Hebbal' },
  'btm_layout': { lat: 12.9165, lng: 77.6101, name: 'BTM Layout' },
  'marathahalli': { lat: 12.9591, lng: 77.6974, name: 'Marathahalli' },
  'rajajinagar': { lat: 12.9915, lng: 77.5552, name: 'Rajajinagar' },
  'jayanagar': { lat: 12.9249, lng: 77.5832, name: 'Jayanagar' },
  'mg_road': { lat: 12.9716, lng: 77.5946, name: 'MG Road' },
  'brigade_road': { lat: 12.9716, lng: 77.6033, name: 'Brigade Road' },
  'silk_board': { lat: 12.9176, lng: 77.6227, name: 'Silk Board' }
};

// Get colleges near a specific location
export const getCollegesNearLocation = (colleges, locationKey, maxDistance = 20) => {
  const location = BANGALORE_LOCATIONS[locationKey];
  if (!location) {
    throw new Error(`Unknown location: ${locationKey}`);
  }

  return filterCollegesByDistance(colleges, location, maxDistance);
};

// Search for location by name
export const searchLocations = (query) => {
  if (!query || query.length < 2) {
    return Object.values(BANGALORE_LOCATIONS);
  }

  const searchTerm = query.toLowerCase();
  return Object.values(BANGALORE_LOCATIONS).filter(location =>
    location.name.toLowerCase().includes(searchTerm)
  );
};

// Get distance matrix for multiple colleges from a point
export const getDistanceMatrix = (colleges, fromPoint) => {
  if (!fromPoint || !colleges) {
    return [];
  }

  return colleges.map(college => {
    const collegeCoords = parseCoordinates(college.coordinates);
    const distance = collegeCoords ? calculateDistance(fromPoint, collegeCoords) : null;
    
    return {
      collegeId: college.id,
      collegeName: college.name,
      distance,
      coordinates: collegeCoords,
      travelTime: distance ? estimateTravelTime(distance) : null
    };
  }).filter(item => item.distance !== null);
};

// Estimate travel time based on distance (rough approximation)
const estimateTravelTime = (distance) => {
  // Assume average speed in Bangalore traffic
  const avgSpeed = 25; // km/h in city traffic
  const timeInHours = distance / avgSpeed;
  const timeInMinutes = Math.round(timeInHours * 60);
  
  return {
    minutes: timeInMinutes,
    formatted: timeInMinutes < 60 
      ? `${timeInMinutes} min` 
      : `${Math.floor(timeInMinutes / 60)}h ${timeInMinutes % 60}m`
  };
};

// Track location usage for analytics
const trackLocationUsage = async (location, maxDistance) => {
  try {
    await addDoc(collection(db, 'locationUsage'), {
      location: {
        lat: location.lat,
        lng: location.lng,
        accuracy: location.accuracy
      },
      maxDistance,
      timestamp: serverTimestamp(),
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : null
    });
  } catch (error) {
    console.error('Error tracking location usage:', error);
  }
};

// Geocode address to coordinates (mock implementation)
export const geocodeAddress = async (address) => {
  // In a real implementation, this would use Google Maps Geocoding API
  // For now, we'll return a mock result for Bangalore addresses
  
  const bangaloreKeywords = ['bangalore', 'bengaluru', 'karnataka'];
  const hasKeyword = bangaloreKeywords.some(keyword => 
    address.toLowerCase().includes(keyword)
  );
  
  if (hasKeyword) {
    // Return approximate Bangalore center coordinates
    return {
      lat: 12.9716,
      lng: 77.5946,
      formatted_address: address,
      accuracy: 'approximate'
    };
  }
  
  throw new Error('Address not found or outside Bangalore');
};

// Check if point is within Bangalore bounds
export const isWithinBangalore = (point) => {
  if (!point || !point.lat || !point.lng) {
    return false;
  }

  // Approximate Bangalore bounds
  const bounds = {
    north: 13.2,
    south: 12.7,
    east: 77.8,
    west: 77.3
  };

  return point.lat >= bounds.south && 
         point.lat <= bounds.north && 
         point.lng >= bounds.west && 
         point.lng <= bounds.east;
};

export default {
  parseCoordinates,
  calculateDistance,
  getCurrentLocation,
  filterCollegesByDistance,
  getCollegesNearMe,
  getCollegesNearLocation,
  searchLocations,
  getDistanceMatrix,
  geocodeAddress,
  isWithinBangalore,
  BANGALORE_LOCATIONS
};
